// Локальный сервер для тестирования API endpoints
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Импортируем наши API handlers
import sendToSheetHandler from './api/send-to-sheet.js';
import cachedScheduleHandler from './api/cached-schedule.js';
import meldteamCharHandler from './api/meldteam-char.js';

// Загружаем переменные окружения
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Логирование запросов
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// API Routes
app.post('/api/send-to-sheet', sendToSheetHandler);
app.get('/api/cached-schedule', cachedScheduleHandler);
app.post('/api/meldteam-char', meldteamCharHandler);

// Корневой маршрут
app.get('/', (req, res) => {
  res.json({
    message: 'TG Server API с кэшированием расписания',
    endpoints: [
      'POST /api/send-to-sheet - Отправка заказов в Google Sheets с корректировкой времени',
      'GET /api/cached-schedule - Расписание с CDN кэшированием (2 часа)',
      'POST /api/meldteam-char - Получение информации о персонаже через MeldTeam API'
    ]
  });
});

// Обработка 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// Обработка ошибок
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    error: 'Internal Server Error',
    message: error.message
  });
});

// Запуск сервера
app.listen(PORT, () => {
  console.log(`🚀 Сервер запущен на порту ${PORT}`);
  console.log(`📡 API доступно по адресу: http://localhost:${PORT}`);
  console.log(`📋 Endpoints:`);
  console.log(`   POST http://localhost:${PORT}/api/send-to-sheet`);
  console.log(`   GET  http://localhost:${PORT}/api/cached-schedule`);
  console.log(`   POST http://localhost:${PORT}/api/meldteam-char`);
  console.log(`💾 Кэш расписания: CDN кэширование (2 часа)`);
  console.log(`🚀 Оптимизировано для Vercel Serverless Functions`);
});

export default app;
