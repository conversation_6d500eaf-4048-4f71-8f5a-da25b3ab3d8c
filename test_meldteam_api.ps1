# PowerShell скрипт для тестирования нашего MeldTeam API endpoint
# Тестирует новый endpoint /api/meldteam-char

Write-Host "Тестирование нашего MeldTeam API endpoint..." -ForegroundColor Green

# URL нашего API endpoint
$API_URL = "http://localhost:3000/api/meldteam-char"

# Тестовые данные (тот же пример что и в test_api.ps1)
$TEST_INPUT = "https://worldofwarcraft.blizzard.com/en-gb/character/eu/pozzo-delleternit%C3%A0/archetta"

# Заголовки запроса
$headers = @{
    "Content-Type" = "application/json"
}

# Тело запроса
$body = @{
    "input" = $TEST_INPUT
} | ConvertTo-Json

Write-Host "Отправляем запрос к нашему API..." -ForegroundColor Yellow
Write-Host "URL: $API_URL" -ForegroundColor Cyan
Write-Host "Input: $TEST_INPUT" -ForegroundColor Cyan

try {
    # Отправка запроса
    $response = Invoke-RestMethod -Uri $API_URL -Method POST -Headers $headers -Body $body
    
    Write-Host ""
    Write-Host "✅ Ответ нашего API:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
    
    # Проверяем структуру ответа
    if ($response.success -eq $true -and $response.data) {
        Write-Host ""
        Write-Host "✅ Структура ответа корректна!" -ForegroundColor Green
        Write-Host "  • success: $($response.success)" -ForegroundColor White
        Write-Host "  • input: $($response.input)" -ForegroundColor White
        Write-Host "  • data.name: $($response.data.name)" -ForegroundColor White
        Write-Host "  • data.other: $($response.data.other.Length) элементов" -ForegroundColor White
    } else {
        Write-Host ""
        Write-Host "⚠️ Неожиданная структура ответа!" -ForegroundColor Yellow
    }
}
catch {
    Write-Host ""
    Write-Host "❌ Ошибка при отправке запроса:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Тест завершен." -ForegroundColor Green
