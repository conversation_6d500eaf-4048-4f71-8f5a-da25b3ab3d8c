function doPost(e) {
  const startTime = new Date();
  try {
    // Логируем входящий запрос с временной меткой
    Logger.log(`[${startTime.toISOString()}] doPost вызван`);

    // Проверяем наличие данных
    if (!e.postData || !e.postData.contents) {
      Logger.log('Нет данных в запросе');
      return ContentService.createTextOutput(JSON.stringify({
        success: false,
        error: 'No post data provided'
      })).setMimeType(ContentService.MimeType.JSON);
    }

    const data = JSON.parse(e.postData.contents);
    Logger.log('Данные получены: ' + JSON.stringify(data));

    if (data.action === 'wowHetz') {
      const nickname = data.nickname;
      const row = data.row;
      const orderData = data.orderData; // Данные заказа для определения где был nickname

      if (!nickname) {
        Logger.log('Nickname не предоставлен');
        return ContentService.createTextOutput(JSON.stringify({
          success: false,
          error: 'Nickname is required'
        })).setMimeType(ContentService.MimeType.JSON);
      }

      if (!row) {
        Logger.log('Row не предоставлен');
        return ContentService.createTextOutput(JSON.stringify({
          success: false,
          error: 'Row is required'
        })).setMimeType(ContentService.MimeType.JSON);
      }

      Logger.log(`Вызываем wowHetz для: ${nickname} (строка: ${row})`);

      const wowHetzStart = new Date();
      const wowResult = wowHetz(nickname);
      const wowHetzTime = new Date() - wowHetzStart;

      Logger.log(`Результат wowHetz за ${wowHetzTime}ms: ${JSON.stringify(wowResult)}`);

      // Записываем результат wowHetz в Google Sheets (логика из старой doPost)
      if (wowResult && row) {
        try {
          const writeStart = new Date();
          writeWowHetzToSheet(wowResult, row, orderData, nickname);
          const writeTime = new Date() - writeStart;
          Logger.log(`Запись результата wowHetz в лист завершена за ${writeTime}ms`);
        } catch (writeError) {
          Logger.log(`Ошибка записи результата wowHetz: ${writeError.toString()}`);
          // Не прерываем выполнение, возвращаем результат даже если запись не удалась
        }
      }

      const totalTime = new Date() - startTime;
      Logger.log(`doPost завершен за ${totalTime}ms`);

      return ContentService.createTextOutput(JSON.stringify({
        success: true,
        result: wowResult,
        nickname: nickname,
        row: row,
        timing: {
          total: totalTime,
          wowHetz: wowHetzTime
        },
        timestamp: data.timestamp || null,
        source: data.source || 'unknown'
      })).setMimeType(ContentService.MimeType.JSON);
    }

    Logger.log('Неизвестное действие: ' + data.action);
    return ContentService.createTextOutput(JSON.stringify({
      success: false,
      error: 'Unknown action: ' + data.action
    })).setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    const totalTime = new Date() - startTime;
    Logger.log(`Ошибка в doPost за ${totalTime}ms: ${error.toString()}`);
    return ContentService.createTextOutput(JSON.stringify({
      success: false,
      error: error.toString(),
      timing: {
        total: totalTime
      }
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

// Функция для записи результата wowHetz в Google Sheets
function writeWowHetzToSheet(wowResult, row, orderData, nickname) {
  try {
    Logger.log(`Записываем результат wowHetz в строку ${row} для ${nickname}`);
    Logger.log(`OrderData: ${JSON.stringify(orderData)}`);

    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName('Oper'); // Используем тестовый лист

    if (!sheet) {
      throw new Error('Лист "Oper TEST" не найден');
    }

    // Читаем текущие данные из строки чтобы определить где был записан nickname
    const currentRowData = sheet.getRange(row, 1, 1, 43).getValues()[0]; // Читаем всю строку

    // Определяем где был nickname - в столбце O (ссылка) или Q (ник)
    // Проверяем что записано в столбцах O (индекс 14) и Q (индекс 16)
    const hasLinkInO = currentRowData[14] && currentRowData[14] !== ""; // Столбец O (15-й столбец, индекс 14)
    const hasNickInQ = currentRowData[16] && currentRowData[16] !== ""; // Столбец Q (17-й столбец, индекс 16)

    Logger.log(`Анализ текущей строки ${row}:`);
    Logger.log(`  Столбец O (индекс 14): "${currentRowData[14]}"`);
    Logger.log(`  Столбец Q (индекс 16): "${currentRowData[16]}"`);
    Logger.log(`  hasLinkInO: ${hasLinkInO}`);
    Logger.log(`  hasNickInQ: ${hasNickInQ}`);

    if (hasLinkInO) {
      // Если ссылка была в столбце O (15)
      Logger.log('Записываем результат wowHetz: ссылка в O, записываем other в столбцы 26-30 и name в столбец 17');

      // Записываем other в столбцы 27-30 (смещение на 1 вправо)
      if (wowResult.other && wowResult.other.length > 0) {
        const targetRangeOther = sheet.getRange(row, 27, 1, 4); // Столбцы 27-30 (4 столбца)
        targetRangeOther.setValues([wowResult.other]);
        Logger.log(`Записали other в столбцы 27-30: ${JSON.stringify(wowResult.other)}`);
      }

      // Записываем name в столбец 17 (offset(0, 2, 1, 1) от столбца O=15 → 15+2=17)
      if (wowResult.name) {
        const targetRangeName = sheet.getRange(row, 17, 1, 1); // Столбец 17
        targetRangeName.setValue(wowResult.name);
        Logger.log(`Записали name в столбец 17: ${wowResult.name}`);
      }

    } else {
      // Если ник был в столбце Q (17)
      Logger.log('Записываем результат wowHetz: ник в Q, записываем other в столбцы 26-30 и очищаем O');

      // Записываем other в столбцы 27-30 (смещение на 1 вправо)
      if (wowResult.other && wowResult.other.length > 0) {
        const targetRangeOther = sheet.getRange(row, 27, 1, 4); // Столбцы 27-30 (4 столбца)
        targetRangeOther.setValues([wowResult.other]);
        Logger.log(`Записали other в столбцы 27-30: ${JSON.stringify(wowResult.other)}`);
      }

      // Очищаем столбец O (offset(0, -2, 1, 1) от столбца Q=17 → 17-2=15)
      const targetRangeClear = sheet.getRange(row, 15, 1, 1); // Столбец O (15)
      targetRangeClear.setValue("");
      Logger.log('Очистили столбец O (15)');
    }

    Logger.log(`Результат wowHetz успешно записан в строку ${row}`);

  } catch (error) {
    Logger.log(`Ошибка записи результата wowHetz в лист: ${error.toString()}`);
    throw error;
  }
}