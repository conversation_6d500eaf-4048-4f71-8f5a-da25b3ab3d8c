# PowerShell скрипт для тестирования API MeldTeam
# Использует те же заголовки, что и функция wowHetz в Google Apps Script

Write-Host "Отправка тестового запроса к API MeldTeam..." -ForegroundColor Green

# URL API
$API_URL = "http://**************:61690/v1/MeldTeam/char"

# Тестовые данные
$TEST_INPUT = "https://worldofwarcraft.blizzard.com/en-gb/character/eu/pozzo-delleternit%C3%A0/archetta"

# Заголовки запроса
$headers = @{
    "Authorization" = "25a3e055-ff39-4f6f-ad41-2cd6509c0673"
    "User-Agent" = "Mozilla/5.0 (compatible; Google-Apps-Script; beanserver; +https://script.google.com; id: UAEmdDd80ZO3QIZWfXDi3SK4w1PtFba3Swt4)"
    "Content-Type" = "application/json"
}

# Тело запроса
$body = @{
    "input" = $TEST_INPUT
} | ConvertTo-Json

try {
    # Отправка запроса
    $response = Invoke-RestMethod -Uri $API_URL -Method POST -Headers $headers -Body $body
    
    Write-Host "Ответ API:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 10
}
catch {
    Write-Host "Ошибка при отправке запроса:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host ""
Write-Host "Тест завершен." -ForegroundColor Green
