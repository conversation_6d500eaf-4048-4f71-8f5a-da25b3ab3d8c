// API endpoint для получения информации о персонаже через MeldTeam API
// Аналог функции wowHetz из Google Apps Script

export default async function handler(req, res) {
  // Разрешаем только POST запросы
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed. Use POST.' 
    });
  }

  try {
    const { input } = req.body;

    // Проверяем наличие входных данных
    if (!input) {
      return res.status(400).json({
        success: false,
        error: 'Input parameter is required'
      });
    }

    console.log(`[MeldTeam API] Запрос для: ${input}`);

    // Подготавливаем запрос к MeldTeam API
    const apiUrl = 'http://**************:61690/v1/MeldTeam/char';
    const headers = {
      'Authorization': '25a3e055-ff39-4f6f-ad41-2cd6509c0673',
      'User-Agent': 'Mozilla/5.0 (compatible; Google-Apps-Script; beanserver; +https://script.google.com; id: UAEmdDd80ZO3QIZWfXDi3SK4w1PtFba3Swt4)',
      'Content-Type': 'application/json'
    };

    const requestBody = {
      input: input
    };

    console.log(`[MeldTeam API] Отправляем запрос к ${apiUrl}`);

    // Отправляем запрос к MeldTeam API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody),
      timeout: 15000 // 15 секунд таймаут
    });

    if (!response.ok) {
      console.error(`[MeldTeam API] HTTP ошибка: ${response.status} ${response.statusText}`);
      return res.status(response.status).json({
        success: false,
        error: `MeldTeam API error: ${response.status} ${response.statusText}`
      });
    }

    const responseText = await response.text();
    console.log(`[MeldTeam API] Получен ответ: ${responseText}`);

    // Обрабатываем специальные ответы
    if (responseText === 'no input') {
      return res.status(400).json({
        success: false,
        error: 'Что то не так с вводом'
      });
    }

    if (responseText === 'Unauthorized') {
      return res.status(401).json({
        success: false,
        error: 'Неавторизированный доступ'
      });
    }

    // Парсим JSON ответ
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseText);
    } catch (parseError) {
      console.error(`[MeldTeam API] Ошибка парсинга JSON: ${parseError.message}`);
      return res.status(500).json({
        success: false,
        error: 'Invalid JSON response from MeldTeam API'
      });
    }

    // Обрабатываем ответ аналогично функции wowHetz
    if (parsedResponse.other && parsedResponse.other[0]) {
      // Форматируем первый элемент other как JSON строку с отступами
      parsedResponse.other[0] = JSON.stringify(parsedResponse.other[0], null, '\t');
    }

    console.log(`[MeldTeam API] Успешно обработан запрос для: ${input}`);

    // Возвращаем результат
    return res.status(200).json({
      success: true,
      data: parsedResponse,
      input: input
    });

  } catch (error) {
    console.error(`[MeldTeam API] Ошибка: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}
