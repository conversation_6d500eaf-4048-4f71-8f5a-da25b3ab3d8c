// Google Sheets API модуль для Node.js сервера
// НЕ загружайте этот файл в Google Apps Script!

import { google } from 'googleapis';
import dotenv from 'dotenv';

dotenv.config();

// Настройка Google Sheets API
const SPREADSHEET_ID = process.env.TEST_SPREADSHEET_ID;
const SHEET_NAME = 'Oper'; // Название листа
const RANGE_CURRENT_WEEK = 'currentWeek'; // Новый диапазон для уникальных заказов
const RANGE_PARTIAL_WEEK = 'partialWeek'; // Новый диапазон для дубликатов orderid

console.log(`[GoogleAPI] 🔍 Инициализация: SPREADSHEET_ID = ${SPREADSHEET_ID}`);
console.log(`[GoogleAPI] 🔍 Инициализация: SHEET_NAME = ${SHEET_NAME}`);
console.log(`[GoogleAPI] 🔍 Инициализация: RANGE_CURRENT_WEEK = ${RANGE_CURRENT_WEEK}`);
console.log(`[GoogleAPI] 🔍 Инициализация: RANGE_PARTIAL_WEEK = ${RANGE_PARTIAL_WEEK}`);

// Инициализация Google APIs (только Sheets API, Apps Script API не работает с Service Accounts)
let sheets;
let auth;

async function initializeGoogleAPI() {
  try {
    // Проверяем наличие переменной окружения
    const serviceAccountKeyString = process.env.GOOGLE_SERVICE_ACCOUNT_KEY;
    if (!serviceAccountKeyString) {
      throw new Error('GOOGLE_SERVICE_ACCOUNT_KEY не найдена в переменных окружения');
    }

    console.log('[GoogleAPI] 🔍 GOOGLE_SERVICE_ACCOUNT_KEY найдена, длина:', serviceAccountKeyString.length);

    // Загружаем Service Account ключ
    const serviceAccountKey = JSON.parse(serviceAccountKeyString);

    console.log('[GoogleAPI] 🔍 Service Account данные:');
    console.log(`[GoogleAPI]   client_email: ${serviceAccountKey.client_email}`);
    console.log(`[GoogleAPI]   project_id: ${serviceAccountKey.project_id}`);
    console.log(`[GoogleAPI]   private_key: ${serviceAccountKey.private_key ? 'найден' : 'НЕ НАЙДЕН'}`);

    auth = new google.auth.GoogleAuth({
      credentials: serviceAccountKey,
      scopes: [
        'https://www.googleapis.com/auth/spreadsheets'  // Только Sheets API
      ]
    });

    sheets = google.sheets({ version: 'v4', auth });
    console.log('[GoogleAPI] ✅ Google Sheets API инициализирован (wowHetz через HTTP)');

    // Проверяем что авторизация работает
    const authClient = await auth.getClient();
    console.log('[GoogleAPI] ✅ Авторизация успешна');

  } catch (error) {
    console.error('[GoogleAPI] ❌ Ошибка инициализации Google API:', error.message);
    if (error.message.includes('JSON')) {
      console.error('[GoogleAPI] 💡 Проверьте что GOOGLE_SERVICE_ACCOUNT_KEY содержит валидный JSON');
    }
    throw error;
  }
}

// Функция для получения следующей пустой строки в именованном диапазоне
// ОПТИМИЗИРОВАННАЯ: поиск снизу вверх до первой заполненной строки
async function getNextEmptyRowInRange(namedRange) {
  try {
    // Получаем все значения из именованного диапазона
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: SPREADSHEET_ID,
      range: namedRange,
    });

    const rangeValues = response.data.values || [];
    console.log(`[GoogleAPI] Именованный диапазон ${namedRange} содержит ${rangeValues.length} строк`);

    // Функция для проверки, пуста ли строка (та же логика что в code2.gs)
    function isRowEmpty(row) {
      if (!row) return true;

      // Проверяем столбец E (индекс 4) - номер заказа
      let cellIsEmpty = !row[4] || row[4] === "" || row[4] === null || row[4] === undefined;

      // Проверяем дополнительные столбцы
      let additionalColumnsEmpty =
        (!row[1] || row[1] === "" || row[1] === null || row[1] === undefined) && // столбец B - дата
        (!row[6] || row[6] === "" || row[6] === null || row[6] === undefined) && // столбец G - магазин
        (!row[16] || row[16] === "" || row[16] === null || row[16] === undefined) && // столбец Q - ник
        (!row[19] || row[19] === "" || row[19] === null || row[19] === undefined) && // столбец T - время
        (!row[20] || row[20] === "" || row[20] === null || row[20] === undefined) && // столбец U - состав
        (!row[21] || row[21] === "" || row[21] === null || row[21] === undefined); // столбец V - задание

      return cellIsEmpty && additionalColumnsEmpty;
    }

    // ОПТИМИЗАЦИЯ: ищем снизу вверх до первой заполненной строки
    let lastFilledRowIndex = -1;

    for (let j = rangeValues.length - 1; j >= 0; j--) {
      const row = rangeValues[j] || [];

      if (!isRowEmpty(row)) {
        lastFilledRowIndex = j;
        console.log(`[GoogleAPI] Последняя заполненная строка найдена в позиции ${j} (поиск снизу вверх)`);
        break;
      }
    }

    // Первая пустая строка = последняя заполненная + 1
    const emptyRowIndex = lastFilledRowIndex + 1;

    // Проверяем, что не вышли за пределы диапазона
    if (emptyRowIndex >= rangeValues.length) {
      throw new Error(`Нет свободных строк в именованном диапазоне ${namedRange}. Все ${rangeValues.length} строк заняты.`);
    }

    console.log(`[GoogleAPI] ОПТИМИЗИРОВАННЫЙ поиск: первая пустая строка в позиции ${emptyRowIndex} (после заполненной ${lastFilledRowIndex})`);
    return emptyRowIndex;
  } catch (error) {
    console.error('[GoogleAPI] Ошибка получения пустой строки в диапазоне:', error);
    throw error;
  }
}

// Функция для записи данных в именованный диапазон (только заполненные ячейки)
async function writeOrderToNamedRange(order, rowIndex, namedRange) {
  try {
    // Используем батчинг даже для одного заказа
    const data = prepareOrderData(order);
    await writeBatchToNamedRange([data], rowIndex, namedRange);

    console.log(`[GoogleAPI] Данные записаны в строку ${rowIndex} именованного диапазона ${namedRange} (только заполненные ячейки)`);
    return rowIndex;
  } catch (error) {
    console.error('[GoogleAPI] Ошибка записи данных в именованный диапазон:', error);
    throw error;
  }
}

// Функция для обновления ссылки в AH2 (на листе Oper)
async function updateLastRowLink(lastRowIndex, startRowIndex = null, namedRange = RANGE_PARTIAL_WEEK) {
  try {
    // Получаем информацию о именованном диапазоне для определения gid листа
    const spreadsheetInfo = await sheets.spreadsheets.get({
      spreadsheetId: SPREADSHEET_ID
    });

    const namedRangeObj = spreadsheetInfo.data.namedRanges?.find(range => range.name === namedRange);
    if (!namedRangeObj) {
      throw new Error(`Именованный диапазон ${namedRange} не найден`);
    }

    const sheetId = namedRangeObj.range.sheetId;
    const rangeStartRow = namedRangeObj.range.startRowIndex; // 0-based

    // Вычисляем реальные номера строк в листе (1-based)
    const lastRealRowNumber = rangeStartRow + lastRowIndex + 1;

    let fullUrl, linkText;

    if (startRowIndex !== null && startRowIndex !== lastRowIndex) {
      // Диапазон строк (для батча заказов)
      const firstRealRowNumber = rangeStartRow + startRowIndex + 1;
      fullUrl = `https://docs.google.com/spreadsheets/d/${SPREADSHEET_ID}/edit#gid=${sheetId}&range=${firstRealRowNumber}:${lastRealRowNumber}`;
      linkText = `Строка ${firstRealRowNumber}-${lastRealRowNumber}`;
    } else {
      // Одна строка
      fullUrl = `https://docs.google.com/spreadsheets/d/${SPREADSHEET_ID}/edit#gid=${sheetId}&range=${lastRealRowNumber}:${lastRealRowNumber}`;
      linkText = `Строка ${lastRealRowNumber}`;
    }

    const linkFormula = `=HYPERLINK("${fullUrl}"; "${linkText}")`;

    await sheets.spreadsheets.values.update({
      spreadsheetId: SPREADSHEET_ID,
      range: `${SHEET_NAME}!AK2`,
      valueInputOption: 'USER_ENTERED',
      resource: {
        values: [[linkFormula]]
      }
    });

    console.log(`[GoogleAPI] Обновлена ссылка в AH2: ${linkText} (gid=${sheetId})`);
  } catch (error) {
    console.error('[GoogleAPI] Ошибка обновления ссылки:', error);
    throw error;
  }
}

// Функция для преобразования индекса в именованном диапазоне в реальный номер строки листа
async function getRealRowNumberFromIndex(rowIndex, namedRange) {
  try {
    // Получаем информацию о именованном диапазоне
    const spreadsheetInfo = await sheets.spreadsheets.get({
      spreadsheetId: SPREADSHEET_ID
    });

    const namedRangeObj = spreadsheetInfo.data.namedRanges?.find(range => range.name === namedRange);
    if (!namedRangeObj) {
      throw new Error(`Именованный диапазон ${namedRange} не найден`);
    }

    const startRow = namedRangeObj.range.startRowIndex; // 0-based
    const realRowNumber = startRow + rowIndex + 1; // +1 для 1-based нумерации Google Sheets

    console.log(`[GoogleAPI] 🔢 Преобразование: индекс ${rowIndex} → реальная строка ${realRowNumber} (startRow=${startRow})`);
    return realRowNumber;
  } catch (error) {
    console.error('[GoogleAPI] Ошибка преобразования номера строки:', error);
    throw error;
  }
}

// Функция для вызова wowHetz через HTTP (doPost) и записи результата
// РЕШЕНИЕ: Apps Script API не работает с Service Accounts, используем HTTP
async function callWowHetzAndWriteResult(nickname, row) {
  try {
    console.log(`[GoogleAPI] 🚀 wowHetz для ${nickname} в строке ${row} (HTTP doPost - СТАБИЛЬНОЕ РЕШЕНИЕ)`);

    // URL вашего Apps Script Web App
    const appsScriptUrl = process.env.WOWHETZ_APPS_SCRIPT_URL;

    if (!appsScriptUrl) {
      console.log(`[GoogleAPI] ⚠️ WOWHETZ_APPS_SCRIPT_URL не настроена`);
      return { success: false, sent: false, error: 'Apps Script URL not configured' };
    }

    console.log(`[GoogleAPI] 🔍 Используем Apps Script URL: ${appsScriptUrl}`);

    // Подготавливаем данные для HTTP запроса
    const requestData = {
      action: 'wowHetz',
      nickname: nickname,
      row: row
      // orderData больше не нужен - Google Apps Script сам читает данные из таблицы
    };

    console.log(`[GoogleAPI] 📤 Отправляем HTTP запрос к Apps Script...`);

    // Отправляем HTTP запрос с таймаутом
    const requestStart = Date.now();
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 секунд таймаут

    try {
      const response = await fetch(appsScriptUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'GoogleSheetsAPI/1.0'
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const requestTime = Date.now() - requestStart;

      if (!response.ok) {
        console.log(`[GoogleAPI] ⚠️ HTTP ошибка ${response.status} для ${nickname} за ${requestTime}ms`);
        return { success: false, sent: true, written: false, error: `HTTP ${response.status}` };
      }

      const result = await response.json();
      console.log(`[GoogleAPI] ✅ wowHetz выполнен для ${nickname} за ${requestTime}ms`);

      if (result.success && result.result) {
        const wowResult = result.result;
        console.log(`[GoogleAPI] 📊 wowHetz результат: name="${wowResult.name}", other=${wowResult.other ? wowResult.other.length : 0} элементов`);

        // Google Apps Script теперь сам записывает результат в лист
        console.log(`[GoogleAPI] ✅ wowHetz выполнен и записан Google Apps Script для ${nickname}`);

        return { success: true, sent: true, written: true };
      } else {
        console.log(`[GoogleAPI] ⚠️ wowHetz ошибка в ответе для ${nickname}: ${result.error || 'неизвестная ошибка'}`);
        return { success: false, sent: true, written: false, error: result.error || 'Invalid response' };
      }

    } catch (fetchError) {
      clearTimeout(timeoutId);
      const requestTime = Date.now() - requestStart;

      if (fetchError.name === 'AbortError') {
        console.log(`[GoogleAPI] ⚠️ wowHetz таймаут для ${nickname} за ${requestTime}ms (15 секунд)`);
        return { success: false, sent: false, written: false, error: 'Timeout after 15 seconds' };
      } else {
        console.log(`[GoogleAPI] ⚠️ wowHetz сетевая ошибка для ${nickname} за ${requestTime}ms: ${fetchError.message}`);
        return { success: false, sent: false, written: false, error: fetchError.message };
      }
    }

  } catch (error) {
    console.log(`[GoogleAPI] ⚠️ wowHetz не удалось отправить для ${nickname}: ${error.message}`);
    return { success: false, sent: false, written: false, error: error.message };
  }
}

// УДАЛЕНО: Функция writeWowHetzResult больше не нужна
// Google Apps Script теперь сам записывает результаты wowHetz в лист

// Функция для получения команды для заказа из уже полученных данных API
function getTeamForOrder(order, teamsData) {
  try {
    console.log(`[TeamAPI] Получаем команду для заказа: дата=${order.date}, время=${order.time}, тип=${order.task}`);

    if (!teamsData || !teamsData.success || !teamsData.data || !Array.isArray(teamsData.data)) {
      console.log('[TeamAPI] Данные Teams API недоступны или неверного формата');
      return '';
    }

    console.log(`[TeamAPI] Обрабатываем ${teamsData.data.length} записей рейдов`);

    // Карта соответствий рейдов и возможных тасков
    const allowedRaidTypes = {
      "MO Heroic": ["MO Heroic", "MO Heroic Unsaved", "Dimensius Heroic", "MO Single Boss HC"],
      "MO Normal": ["MO Normal", "MO Normal Unsaved", "Dimensius Normal", "MO Single Boss NM"],
      "MO Heroic Unsaved": ["MO Heroic", "MO Heroic Unsaved", "Dimensius Heroic", "MO Single Boss HC"],
      "MO Normal Unsaved": ["MO Normal", "MO Normal Unsaved", "Dimensius Normal", "MO Single Boss NM"]
    };

    // Ищем подходящий рейд по дате, времени и типу заказа
    const matchingRaids = teamsData.data.filter(raid => {
      const dateMatch = raid.date === order.date;
      const timeMatch = raid.time === order.time;

      // Проверяем соответствие типа заказа типу рейда по карте
      let typeMatch = false;
      if (order.task && raid.raidType) {
        const allowedTasks = allowedRaidTypes[raid.raidType];
        if (allowedTasks && allowedTasks.includes(order.task)) {
          typeMatch = true;
        }
      }

      return dateMatch && timeMatch && typeMatch;
    });

    if (matchingRaids.length === 0) {
      console.log(`[TeamAPI] ❌ Не найдено подходящих рейдов для: дата=${order.date}, время=${order.time}, тип=${order.task}`);

      // Показываем доступные рейды для отладки
      const raidsForDateTime = teamsData.data.filter(raid =>
        raid.date === order.date && raid.time === order.time
      );

      if (raidsForDateTime.length > 0) {
        console.log(`[TeamAPI] 📋 Доступные рейды на ${order.date} ${order.time}:`);
        raidsForDateTime.forEach(raid => {
          const allowedTasks = allowedRaidTypes[raid.raidType] || [];
          console.log(`[TeamAPI]   - ${raid.raidType} (разрешенные таски: ${allowedTasks.join(', ')})`);
        });
      } else {
        console.log(`[TeamAPI] 📋 Нет рейдов на ${order.date} ${order.time}`);
      }

      return '';
    }

    console.log(`[TeamAPI] Найдено ${matchingRaids.length} подходящих рейдов`);

    // Если найдено несколько рейдов, выбираем наиболее точное совпадение
    let selectedRaid = null;

    if (matchingRaids.length === 1) {
      selectedRaid = matchingRaids[0];
      console.log(`[TeamAPI] Выбран единственный подходящий рейд: ${selectedRaid.raidType}`);
    } else if (matchingRaids.length > 1) {
      // Если несколько рейдов, приоритизируем по приоритету типов
      // Приоритет: точное совпадение > Unsaved версии > остальные

      // Сначала ищем точное совпадение названия рейда с таском
      const exactMatches = matchingRaids.filter(raid => raid.raidType === order.task);

      if (exactMatches.length > 0) {
        selectedRaid = exactMatches[0];
        console.log(`[TeamAPI] Выбран рейд с точным совпадением названия: ${selectedRaid.raidType}`);
      } else {
        // Если точного совпадения нет, выбираем по приоритету:
        // 1. Обычные рейды (без Unsaved)
        // 2. Unsaved рейды
        const nonUnsavedRaids = matchingRaids.filter(raid => !raid.raidType.includes('Unsaved'));

        if (nonUnsavedRaids.length > 0) {
          selectedRaid = nonUnsavedRaids[0];
          console.log(`[TeamAPI] Выбран обычный рейд (не Unsaved): ${selectedRaid.raidType} из ${nonUnsavedRaids.length} вариантов`);
        } else {
          selectedRaid = matchingRaids[0];
          console.log(`[TeamAPI] Выбран первый подходящий рейд: ${selectedRaid.raidType}`);
        }
      }
    }

    if (!selectedRaid || !selectedRaid.expectedTeams || !Array.isArray(selectedRaid.expectedTeams)) {
      console.log('[TeamAPI] Выбранный рейд не содержит команд');
      return '';
    }

    console.log(`[TeamAPI] В выбранном рейде ${selectedRaid.raidType} найдено ${selectedRaid.expectedTeams.length} команд`);

    // Находим команду с наименьшим количеством клиентов в выбранном рейде
    let minClientCount = Infinity;
    let selectedTeam = null;

    selectedRaid.expectedTeams.forEach((team, index) => {
      const clientCount = team.clientCount || 0;
      console.log(`[TeamAPI] Команда ${index + 1}: ${team.team} (клиентов: ${clientCount})`);

      if (clientCount < minClientCount) {
        minClientCount = clientCount;
        selectedTeam = team;
      }
    });

    if (selectedTeam) {
      console.log(`[TeamAPI] Выбрана команда: ${selectedTeam.team} (клиентов: ${selectedTeam.clientCount || 0}) из рейда ${selectedRaid.raidType}`);
      return selectedTeam.team;
    } else {
      console.log('[TeamAPI] Не удалось выбрать команду');
      return '';
    }

  } catch (error) {
    console.error('[TeamAPI] Ошибка при получении команды:', error);
    return '';
  }
}

// Функция для получения команды с наименьшим количеством клиентов (устаревшая, оставлена для совместимости)
async function getTeamWithMinClients(order) {
  try {
    console.log(`[TeamAPI] Получаем команду для заказа: дата=${order.date}, время=${order.time}, тип=${order.task}`);

    const response = await fetch('http://138.201.175.112:61690/teams?authorization=jqiwejoi3128', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10 секунд таймаут
    });

    if (!response.ok) {
      console.error(`[TeamAPI] Ошибка HTTP: ${response.status} ${response.statusText}`);
      return '';
    }

    const data = await response.json();

    if (!data.success || !data.data || !Array.isArray(data.data)) {
      console.error('[TeamAPI] Неверный формат ответа от API');
      return '';
    }

    console.log(`[TeamAPI] Получено ${data.data.length} записей рейдов`);

    // Ищем подходящий рейд по дате, времени и типу заказа
    const matchingRaids = data.data.filter(raid => {
      const dateMatch = raid.date === order.date;
      const timeMatch = raid.time === order.time;

      console.log(`[TeamAPI] Проверяем рейд: дата=${raid.date} (совпадение: ${dateMatch}), время=${raid.time} (совпадение: ${timeMatch}), тип=${raid.raidType}`);

      // Сопоставляем тип заказа с типом рейда
      let typeMatch = false;
      if (order.task) {
        const taskLower = order.task.toLowerCase();
        const raidTypeLower = raid.raidType.toLowerCase();

        // Логика сопоставления типов
        if (taskLower.includes('heroic') && raidTypeLower.includes('heroic')) {
          typeMatch = true;
        } else if (taskLower.includes('normal') && raidTypeLower.includes('normal')) {
          typeMatch = true;
        } else if (taskLower.includes('mo') && raidTypeLower.includes('mo')) {
          typeMatch = true;
        } else if (taskLower.includes('dimensius') && raidTypeLower.includes('mo')) {
          // Dimensius заказы сопоставляем с MO рейдами
          if ((taskLower.includes('heroic') && raidTypeLower.includes('heroic')) ||
              (taskLower.includes('normal') && raidTypeLower.includes('normal'))) {
            typeMatch = true;
          }
        }
      }

      return dateMatch && timeMatch && typeMatch;
    });

    if (matchingRaids.length === 0) {
      console.log(`[TeamAPI] Не найдено подходящих рейдов для: дата=${order.date}, время=${order.time}, тип=${order.task}`);
      return '';
    }

    console.log(`[TeamAPI] Найдено ${matchingRaids.length} подходящих рейдов`);

    // Собираем все команды из всех подходящих рейдов
    let allTeams = [];
    matchingRaids.forEach(raid => {
      if (raid.expectedTeams && Array.isArray(raid.expectedTeams)) {
        allTeams = allTeams.concat(raid.expectedTeams);
      }
    });

    if (allTeams.length === 0) {
      console.log('[TeamAPI] Не найдено команд в подходящих рейдах');
      return '';
    }

    // Находим команду с наименьшим количеством клиентов
    let minClientCount = Infinity;
    let selectedTeam = null;

    allTeams.forEach(team => {
      const clientCount = team.clientCount || 0;
      if (clientCount < minClientCount) {
        minClientCount = clientCount;
        selectedTeam = team;
      }
    });

    if (selectedTeam) {
      console.log(`[TeamAPI] Выбрана команда: ${selectedTeam.team} (клиентов: ${selectedTeam.clientCount || 0})`);
      return selectedTeam.team;
    } else {
      console.log('[TeamAPI] Не удалось выбрать команду');
      return '';
    }

  } catch (error) {
    console.error('[TeamAPI] Ошибка при получении команды:', error);
    return '';
  }
}

// Функция для подготовки данных заказа (без записи)
function prepareOrderData(order) {
  const data = [
    order.original || '',           // A - Исходный текст
    order.date || '',              // B - Дата
    '',                            // C - Нужны ли скрины
    '',                            // D - Пустая
    order.orderid || '',           // E - Номер заказа
    '',                            // F - Пустая
    order.shop || '',              // G - Магазин
    '',                            // H - Пустая
    '',                            // I - Итого
    order.priceUsd ? Number(order.priceUsd) : '', // J - USD
    order.priceEur ? Number(order.priceEur) : '', // K - EUR
    order.priceRub ? Number(order.priceRub) : '', // L - RUB
    '',                            // M - Орда/Али/ВнеРТО
    '',                            // N - Пустая
    '',                            // O - Ссылка (если nickname - ссылка)
    '',                            // P - Пустая
    '',                            // Q - Ник (если nickname - текст)
    '',                            // R - НЕ ТРОГАЕМ ЭТОТ СТОЛБЕЦ!
    '',                            // S - Пустая
    order.time || '',              // T - Время
    order.team || '',              // U - Состав (команда)
    order.task || '',              // V - Фулл/Боссы
    '',                            // W - Шара
    order.pilotinfo || '',         // X - Инфо шары
    '',                            // Y - ШАРЫ ОТПРАВКА
    '',                            // Z - Пустая
    '',                            // AA - Пустая
    '',                            // AB - API Error
    '',                            // AC - Пустая
    '',                            // AD - Пустая
    '',                            // AE - тип брони
    '',                            // AF - Пустая
    '',                            // AG - Количество ключей
    '',                            // AH - Ключ
    '',                            // AI - Уровень
    '',                            // AJ - Трейдеры
    order.bosses + ' ' + order.geminiComment ||'',            // AK - пометка (bosses)
    '',                            // AL - кака
    '',                            // AM - Ком от РЛа
    '',                            // AN - Диск
    order.reaction_user || '',     // AO - Кто опубликовал
    '',                            // AP - Кто скинул в архив
    order.aiRawResponse || '',     // AQ - Сырой ответ от ИИ
    '',                            // AR - Пустая
    '',                            // AS - Пустая
    ''                             // AT - Пометка
  ];

  // Обработка nickname - определяем является ли он ссылкой
  const nickname = order.nickname || '';
  if (nickname.toLowerCase().includes('http')) {
    data[14] = nickname; // O - ссылка
    data[16] = '';       // Q - пустое
  } else {
    data[14] = '';       // O - пустое
    data[16] = nickname; // Q - ник
  }

  return data;
}

// Функция для записи батча данных в именованный диапазон (только заполненные ячейки)
async function writeBatchToNamedRange(batchData, startRowIndex, namedRange) {
  try {
    // Получаем информацию о именованном диапазоне
    const spreadsheetInfo = await sheets.spreadsheets.get({
      spreadsheetId: SPREADSHEET_ID
    });

    const namedRangeObj = spreadsheetInfo.data.namedRanges?.find(range => range.name === namedRange);
    if (!namedRangeObj) {
      throw new Error(`Именованный диапазон ${namedRange} не найден`);
    }

    const sheetId = namedRangeObj.range.sheetId;
    const startRow = namedRangeObj.range.startRowIndex;
    const startCol = namedRangeObj.range.startColumnIndex;
    const targetStartRow = startRow + startRowIndex;

    // Функция для преобразования номера столбца в букву
    function columnToLetter(column) {
      let temp, letter = '';
      while (column > 0) {
        temp = (column - 1) % 26;
        letter = String.fromCharCode(temp + 65) + letter;
        column = (column - temp - 1) / 26;
      }
      return letter;
    }

    // Находим название листа
    const sheetName = spreadsheetInfo.data.sheets.find(s => s.properties.sheetId === sheetId)?.properties.title;

    // Определяем какие столбцы нужно записывать (только заполненные)
    const columnsToWrite = [
      { index: 0, name: 'A' },   // original
      { index: 1, name: 'B' },   // date
      { index: 4, name: 'E' },   // orderid
      { index: 6, name: 'G' },   // shop
      { index: 9, name: 'J' },   // priceUsd
      { index: 10, name: 'K' },  // priceEur
      { index: 11, name: 'L' },  // priceRub
      { index: 14, name: 'O' },  // ссылка
      { index: 16, name: 'Q' },  // ник
      // { index: 17, name: 'R' },  // НЕ ТРОГАЕМ СТОЛБЕЦ R!
      { index: 19, name: 'T' },  // time
      { index: 20, name: 'U' },  // team (состав)
      { index: 21, name: 'V' },  // task
      { index: 23, name: 'X' },  // pilotinfo
      { index: 36, name: 'AK' }, // bosses
      { index: 40, name: 'AO' }, // reaction_user
      { index: 41, name: 'AP' }, // geminiComment
      { index: 42, name: 'AQ' }  // aiRawResponse
    ];

    console.log(`[GoogleAPI] Записываем БАТЧ: ${batchData.length} строк в ${columnsToWrite.length} столбцов (только заполненные)`);

    // Подготавливаем запросы для каждого столбца
    const batchRequests = [];

    for (const column of columnsToWrite) {
      // Собираем данные для этого столбца из всех строк
      const columnData = batchData.map(row => [row[column.index] || '']);

      // Пропускаем столбцы где все значения пустые
      const hasData = columnData.some(cell => cell[0] && cell[0] !== '');
      if (!hasData) continue;

      const colLetter = columnToLetter(startCol + column.index + 1);
      const range = `${sheetName}!${colLetter}${targetStartRow + 1}:${colLetter}${targetStartRow + batchData.length}`;

      batchRequests.push({
        range: range,
        values: columnData
      });
    }

    console.log(`[GoogleAPI] Подготовлено ${batchRequests.length} запросов для записи столбцов`);

    // Разделяем запросы на RAW (для текста) и USER_ENTERED (для дат, ID заказов и цен)
    if (batchRequests.length > 0) {
      const apiCallsStart = Date.now();
      const userEnteredRequests = []; // Даты, ID заказов и цены
      const rawRequests = []; // Текстовые данные

      batchRequests.forEach(request => {
        // Проверяем если это столбец B (дата), E (orderid) или J,K,L (цены)
        if (request.range.includes('!B') ||
            request.range.includes('!E') ||
            request.range.includes('!J') ||
            request.range.includes('!K') ||
            request.range.includes('!L')) {
          userEnteredRequests.push(request); // Даты, ID заказов и цены в USER_ENTERED
        } else if (request.range.includes('!T')) {
          // Столбец T (время) обрабатываем отдельно с форматированием
          // Не добавляем в rawRequests
        } else {
          rawRequests.push(request);
        }
      });

      // ОПТИМИЗАЦИЯ: Записываем ВСЕ данные параллельно (включая время с форматированием)
      const dataPromises = [];

      // Записываем текстовые данные с RAW (кроме времени, дат, ID заказов и цен)
      if (rawRequests.length > 0) {
        dataPromises.push(
          sheets.spreadsheets.values.batchUpdate({
            spreadsheetId: SPREADSHEET_ID,
            resource: {
              valueInputOption: 'RAW',
              data: rawRequests
            }
          })
        );
      }

      // Записываем даты, ID заказов и цены с USER_ENTERED (правильная интерпретация)
      if (userEnteredRequests.length > 0) {
        dataPromises.push(
          sheets.spreadsheets.values.batchUpdate({
            spreadsheetId: SPREADSHEET_ID,
            resource: {
              valueInputOption: 'USER_ENTERED',
              data: userEnteredRequests
            }
          })
        );
      }

      // Добавляем время с форматированием в параллельные запросы
      const timeRequests = batchRequests.filter(request => request.range.includes('!T'));
      if (timeRequests.length > 0) {
        console.log(`[GoogleAPI] Добавляем время с форматированием в параллельные запросы (включая пустые ячейки)`);

        const timeColumnIndex = startCol + 19; // Столбец T (индекс 19)

        // Подготавливаем данные времени с форматированием
        const timeRows = [];
        let filledCells = 0;
        let emptyCells = 0;

        for (let i = 0; i < batchData.length; i++) {
          const timeValue = batchData[i][19]; // Столбец T (индекс 19)
          if (timeValue && timeValue !== '') {
            // Ячейка с временем - значение + текстовое форматирование
            timeRows.push({
              values: [{
                userEnteredValue: { stringValue: timeValue },
                userEnteredFormat: {
                  numberFormat: { type: 'TEXT' }
                }
              }]
            });
            filledCells++;
          } else {
            // Пустая ячейка - только текстовое форматирование (без значения)
            timeRows.push({
              values: [{
                userEnteredFormat: {
                  numberFormat: { type: 'TEXT' }
                }
              }]
            });
            emptyCells++;
          }
        }

        console.log(`[GoogleAPI] Время: ${filledCells} заполненных, ${emptyCells} пустых ячеек (все получат текстовое форматирование)`);

        // Добавляем время в параллельные запросы
        dataPromises.push(
          sheets.spreadsheets.batchUpdate({
            spreadsheetId: SPREADSHEET_ID,
            resource: {
              requests: [{
                updateCells: {
                  range: {
                    sheetId: sheetId,
                    startRowIndex: targetStartRow,
                    endRowIndex: targetStartRow + batchData.length,
                    startColumnIndex: timeColumnIndex,
                    endColumnIndex: timeColumnIndex + 1
                  },
                  rows: timeRows,
                  fields: 'userEnteredValue,userEnteredFormat.numberFormat'
                }
              }]
            }
          })
        );
      }

      // ЭТАП 1: Выполняем ВСЕ запросы параллельно (данные + время + форматирование)
      if (dataPromises.length > 0) {
        const dataStart = Date.now();
        await Promise.all(dataPromises);
        const dataTime = Date.now() - dataStart;
        console.log(`[GoogleAPI] ⚡ ВСЕ данные записаны параллельно (${dataPromises.length} запросов) за ${dataTime}ms`);
      }

      // ЭТАП 2 больше не нужен - время обрабатывается в параллельных запросах

      const totalApiTime = Date.now() - apiCallsStart;
      console.log(`[GoogleAPI] 📊 Общее время API операций: ${totalApiTime}ms (данные + форматирование)`);
    }

    const targetEndRow = targetStartRow + batchData.length - 1;
    console.log(`[GoogleAPI] БАТЧ записан успешно: ${batchData.length} заказов в строки ${targetStartRow + 1}-${targetEndRow + 1} (только заполненные столбцы)`);
    return { startRow: targetStartRow, endRow: targetEndRow };
  } catch (error) {
    console.error('[GoogleAPI] Ошибка записи батча:', error);
    throw error;
  }
}

// Оптимизированная функция обработки заказов (батчинг)
async function processOrdersWithAPI(orders) {
  try {
    // Инициализируем API если еще не инициализирован
    if (!sheets) {
      await initializeGoogleAPI();
    }

    console.log(`[GoogleAPI] Начинаем БЫСТРУЮ обработку ${orders.length} заказов`);

    if (orders.length === 1) {
      // Для одного заказа используем currentWeek
      return await processSingleOrder(orders[0], RANGE_CURRENT_WEEK);
    }

    // Для множественных заказов используем новую логику
    return await processBatchOrdersSmart(orders);

  } catch (error) {
    console.error('[GoogleAPI] Ошибка обработки заказов:', error);
    throw error;
  }
}

// Обработка одного заказа (ОПТИМИЗИРОВАННАЯ)
async function processSingleOrder(order, namedRange) {
  console.log(`[GoogleAPI] Обрабатываем 1 заказ (оптимизированная логика)`);

  const writeStartTime = Date.now();
  const targetRowIndex = await getNextEmptyRowInRange(namedRange);
  await writeOrderToNamedRange(order, targetRowIndex, namedRange);
  const writeTime = Date.now() - writeStartTime;
  console.log(`[GoogleAPI] Запись данных завершена за ${writeTime}ms`);

  const linkStartTime = Date.now();
  await updateLastRowLink(targetRowIndex, null, namedRange); // Для одного заказа startRowIndex не передаем
  const linkTime = Date.now() - linkStartTime;
  console.log(`[GoogleAPI] Обновление ссылки завершено за ${linkTime}ms`);

  // Основная обработка завершена - фиксируем время
  const mainProcessingEndTime = Date.now();

  // wowHetz с записью результата для ФИНАЛЬНОГО nickname
  // Получаем финальный nickname из записанных данных (после всех обработок)
  const orderData = prepareOrderData(order);
  const finalNickname = orderData[14] || orderData[16]; // O (ссылка) или Q (ник)

  let wowHetzResult = null;
  if (finalNickname && finalNickname.trim()) {
    console.log(`[GoogleAPI] Вызываем wowHetz с записью результата для ФИНАЛЬНОГО nickname: ${finalNickname}`);
    try {
      // Вычисляем реальный номер строки в листе (targetRowIndex + 1 для 1-based нумерации + смещение диапазона)
      const realRowNumber = await getRealRowNumberFromIndex(targetRowIndex, namedRange);
      wowHetzResult = await callWowHetzAndWriteResult(finalNickname, realRowNumber);
    } catch (wowError) {
      console.log(`[GoogleAPI] ⚠️ Ошибка wowHetz для ${finalNickname}: ${wowError.message}`);
      wowHetzResult = { success: false, error: wowError.message };
    }
  }

  return {
    success: true,
    ordersProcessed: 1,
    lastRow: targetRowIndex,
    method: 'single-optimized',
    mainProcessingEndTime: mainProcessingEndTime, // Время окончания основной обработки
    timing: {
      dataWrite: writeTime,
      linkUpdate: linkTime,
      wowHetz: wowHetzResult ? (wowHetzResult.success ? 'written' : 'error') : 'none'
    },
    wowHetzResult: wowHetzResult
  };
}

// Новый умный батчинг для множественных заказов
async function processBatchOrdersSmart(orders) {
  console.log(`[GoogleAPI] Используем умный батчинг для ${orders.length} заказов`);
  // Группируем заказы по orderid
  const orderidMap = new Map();
  for (const order of orders) {
    if (!order.orderid) continue;
    if (!orderidMap.has(order.orderid)) {
      orderidMap.set(order.orderid, []);
    }
    orderidMap.get(order.orderid).push(order);
  }
  // Разделяем на группы
  const partialWeekOrders = [];
  const currentWeekOrders = [];
  for (const order of orders) {
    if (order.orderid && orderidMap.get(order.orderid).length > 1) {
      partialWeekOrders.push(order);
    } else {
      currentWeekOrders.push(order);
    }
  }
  const results = [];
  // Сначала partialWeek
  if (partialWeekOrders.length > 0) {
    results.push(await processBatchOrders(partialWeekOrders, RANGE_PARTIAL_WEEK));
  }
  // Потом currentWeek
  if (currentWeekOrders.length > 0) {
    results.push(await processBatchOrders(currentWeekOrders, RANGE_CURRENT_WEEK));
  }
  return results.length === 1 ? results[0] : { success: true, results };
}

// Батчинг для множественных заказов (ОПТИМИЗИРОВАННЫЙ)
async function processBatchOrders(orders, namedRange) {
  console.log(`[GoogleAPI] Используем ОПТИМИЗИРОВАННЫЙ БАТЧИНГ для ${orders.length} заказов в диапазон ${namedRange}`);

  // 1. Находим первую пустую строку
  const startRowIndex = await getNextEmptyRowInRange(namedRange);
  console.log(`[GoogleAPI] Начальная строка для батча: ${startRowIndex}`);

  // 2. Подготавливаем все данные заказов
  const batchData = orders.map(order => prepareOrderData(order));
  console.log(`[GoogleAPI] Подготовлено ${batchData.length} строк данных`);

  // 3. Записываем ВСЕ заказы ОДНИМ запросом
  const writeStartTime = Date.now();
  await writeBatchToNamedRange(batchData, startRowIndex, namedRange);
  const writeTime = Date.now() - writeStartTime;
  console.log(`[GoogleAPI] Запись данных завершена за ${writeTime}ms`);

  // 4. Обновляем ссылку на диапазон строк
  const linkStartTime = Date.now();
  const lastRowIndex = startRowIndex + orders.length - 1;
  await updateLastRowLink(lastRowIndex, startRowIndex, namedRange); // Передаем startRowIndex для диапазона
  const linkTime = Date.now() - linkStartTime;
  console.log(`[GoogleAPI] Обновление ссылки завершено за ${linkTime}ms`);

  // Основная обработка завершена - фиксируем время
  const mainProcessingEndTime = Date.now();

  // 5. wowHetz с записью результатов для каждого ФИНАЛЬНОГО nickname
  // ОПТИМИЗАЦИЯ: Выполняем все wowHetz запросы ПАРАЛЛЕЛЬНО вместо последовательно
  const wowHetzPromises = [];
  for (let index = 0; index < orders.length; index++) {
    const order = orders[index];
    // Получаем финальный nickname из подготовленных данных (после всех обработок)
    const orderData = prepareOrderData(order);
    const finalNickname = orderData[14] || orderData[16]; // O (ссылка) или Q (ник)

    if (finalNickname && finalNickname.trim()) {
      const rowIndex = startRowIndex + index;
      console.log(`[GoogleAPI] Вызываем wowHetz ПАРАЛЛЕЛЬНО для ФИНАЛЬНОГО nickname: ${finalNickname}`);

      // Создаем промис для параллельного выполнения
      const wowHetzPromise = (async () => {
        try {
          // Вычисляем реальный номер строки в листе
          const realRowNumber = await getRealRowNumberFromIndex(rowIndex, namedRange);
          const wowResult = await callWowHetzAndWriteResult(finalNickname, realRowNumber);
          return { nickname: finalNickname, row: realRowNumber, result: wowResult };
        } catch (wowError) {
          console.log(`[GoogleAPI] ⚠️ Ошибка wowHetz для ${finalNickname}: ${wowError.message}`);
          return { nickname: finalNickname, row: rowIndex, result: { success: false, error: wowError.message } };
        }
      })();

      wowHetzPromises.push(wowHetzPromise);
    }
  }

  // Ожидаем завершения ВСЕХ wowHetz запросов параллельно
  let wowHetzResults = [];
  if (wowHetzPromises.length > 0) {
    console.log(`[GoogleAPI] 🚀 Выполняем ${wowHetzPromises.length} wowHetz запросов ПАРАЛЛЕЛЬНО`);
    const wowHetzStart = Date.now();

    try {
      wowHetzResults = await Promise.all(wowHetzPromises);
      const wowHetzTime = Date.now() - wowHetzStart;
      console.log(`[GoogleAPI] ⚡ Все wowHetz завершены параллельно за ${wowHetzTime}ms`);
    } catch (error) {
      console.log(`[GoogleAPI] ⚠️ Ошибка в параллельных wowHetz запросах: ${error.message}`);
      // Если Promise.all упал, пробуем получить частичные результаты
      wowHetzResults = await Promise.allSettled(wowHetzPromises).then(results =>
        results.map(result => result.status === 'fulfilled' ? result.value : { result: { success: false, error: 'Promise failed' } })
      );
    }
  }

  if (wowHetzResults.length > 0) {
    const successful = wowHetzResults.filter(r => r.result.success).length;
    const failed = wowHetzResults.filter(r => !r.result.success).length;
    console.log(`[GoogleAPI] wowHetz завершен: ${successful} успешно, ${failed} ошибок`);
  }

  console.log(`[GoogleAPI] Батч из ${orders.length} заказов обработан успешно (С ПАРАЛЛЕЛЬНОЙ ЗАПИСЬЮ wowHetz)`);
  return {
    success: true,
    ordersProcessed: orders.length,
    lastRow: lastRowIndex,
    method: 'batch-optimized-parallel-wowhetz',
    mainProcessingEndTime: mainProcessingEndTime, // Время окончания основной обработки
    timing: {
      dataWrite: writeTime,
      linkUpdate: linkTime,
      wowHetz: wowHetzResults.length > 0 ? 'written-parallel' : 'none'
    },
    wowHetzResults: wowHetzResults
  };
}

// Экспорт для ES модулей
export { processOrdersWithAPI, getTeamWithMinClients, getTeamForOrder };
